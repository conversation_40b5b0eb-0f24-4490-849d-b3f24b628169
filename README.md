# 今日头条微头条自动发布工具

使用 Playwright 自动化发布今日头条微头条的工具。

## 安装依赖

```bash
npm install
npm run install-browsers
```

## 使用方法

1. 运行脚本：
```bash
npm run publish
```

2. 浏览器会自动打开，请手动完成登录过程

3. 登录完成后，脚本会自动发布内容"你好,世界"

## 注意事项

- 首次使用需要手动登录今日头条账号
- 脚本会以非无头模式运行，您可以看到整个操作过程
- 如果遇到验证码或其他安全检查，请手动处理
- 发布频率不要过高，避免被平台限制

## 自定义内容

要发布不同的内容，可以修改 `publish-toutiao.js` 文件中的：

```javascript
await publisher.publishMicroPost('你好,世界'); // 修改这里的内容
```

## 故障排除

- 如果元素定位失败，可能是页面结构发生了变化，需要更新选择器
- 错误发生时会自动截图保存为 `error-screenshot.png`
- 建议在开发者工具中检查页面元素，确保选择器正确

## 免责声明

- 请遵守今日头条的使用条款
- 本工具仅用于学习和研究目的
- 使用前请确保符合相关法律法规