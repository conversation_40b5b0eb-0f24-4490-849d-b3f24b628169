const { chromium } = require("playwright");

class ToutiaoPublisher {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async init() {
    this.browser = await chromium.launch({
      headless: false,
      slowMo: 1000, // 减慢操作速度，避免被检测
    });
    this.page = await this.browser.newPage();

    // 设置用户代理
    await this.page.setExtraHTTPHeaders({
      "User-Agent":
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    });
  }

  async login() {
    console.log("正在打开今日头条登录页面...");
    await this.page.goto("https://www.toutiao.com/");

    // 等待登录按钮出现并点击
    await this.page.waitForTimeout(3000);

    try {
      // 寻找登录按钮
      const loginButton = await this.page.locator("text=登录").first();
      if (await loginButton.isVisible()) {
        await loginButton.click();
        console.log("请在浏览器中手动完成登录...");

        // 等待用户手动登录完成，检测是否出现用户头像或用户名
        await this.page.waitForFunction(
          () => {
            // 检查是否有用户头像或者已登录的标识
            return (
              document.querySelector(".user-info") ||
              document.querySelector(".avatar") ||
              document.querySelector('[data-testid="user-avatar"]') ||
              window.location.href.includes("profile")
            );
          },
          { timeout: 60000 }
        );

        console.log("登录成功！");
      }
    } catch (error) {
      console.log("可能已经登录或需要手动登录，继续执行...");
    }
  }

  async publishMicroPost(content) {
    console.log("正在发布微头条...");

    try {
      // 导航到发布页面
      await this.page.goto("https://mp.toutiao.com/profile_v4/graphic/publish");
      await this.page.waitForTimeout(3000);

      // 寻找微头条发布入口
      const microPostTab = await this.page.locator("text=微头条").first();
      if (await microPostTab.isVisible()) {
        await microPostTab.click();
        await this.page.waitForTimeout(2000);
      }

      // 寻找内容输入框
      const contentInput = await this.page
        .locator('textarea, [contenteditable="true"]')
        .first();
      await contentInput.waitFor({ state: "visible", timeout: 10000 });

      // 输入内容
      await contentInput.fill(content);
      console.log(`已输入内容: ${content}`);

      await this.page.waitForTimeout(2000);

      // 寻找发布按钮
      const publishButton = await this.page.locator("text=发布").first();
      await publishButton.waitFor({ state: "visible", timeout: 5000 });

      console.log("点击发布按钮...");
      await publishButton.click();

      // 等待发布完成
      await this.page.waitForTimeout(3000);

      console.log("微头条发布成功！");
    } catch (error) {
      console.error("发布过程中出现错误:", error.message);
      console.log("请检查页面元素是否有变化，可能需要更新选择器");

      // 截图以便调试
      await this.page.screenshot({ path: "error-screenshot.png" });
      console.log("已保存错误截图: error-screenshot.png");
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// 主函数
async function main() {
  const publisher = new ToutiaoPublisher();

  try {
    await publisher.init();
    await publisher.login();
    await publisher.publishMicroPost("你好,世界");

    // 等待一段时间以便查看结果
    await new Promise((resolve) => setTimeout(resolve, 5000));
  } catch (error) {
    console.error("执行过程中出现错误:", error);
  } finally {
    await publisher.close();
  }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
  main();
}

module.exports = ToutiaoPublisher;
